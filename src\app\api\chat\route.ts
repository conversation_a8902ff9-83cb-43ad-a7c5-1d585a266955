
import { createDeepSeek } from '@ai-sdk/deepseek';
import { streamText } from 'ai';
import { prisma } from '../../../../lib/database';
import { auth } from '@clerk/nextjs/server';

export const maxDuration = 30;

export async function POST(req: Request) {
  try {
    const { userId } = await auth();
    if (!userId) return Response.json({ error: '未授权' }, { status: 401 });

    const { messages, conversationId } = await req.json();

    let currentConversationId = conversationId;

    // 如果没有提供对话ID，创建一个新对话
    if (!currentConversationId) {
      // 检查用户是否存在，如果不存在则创建
      let user = await prisma.user.findUnique({
        where: { id: userId }
      });

      if (!user) {
        user = await prisma.user.create({
          data: {
            id: userId,
            email: `${userId}@default.com`,
          }
        });
      }

      // 根据用户消息生成标题
      const lastMessage = messages[messages.length - 1];
      const title = lastMessage.content.length > 20
        ? lastMessage.content.substring(0, 20) + '...'
        : lastMessage.content;

      const newConversation = await prisma.conversation.create({
        data: {
          title: title,
          userId: userId,
        },
      });

      currentConversationId = newConversation.id;
    }

    // 保存用户消息到数据库
    const lastMessage = messages[messages.length - 1];
    await prisma.message.create({
      data: {
        role: 'user',
        content: lastMessage.content,
        conversationId: currentConversationId,
      }
    });

    const deepseek = createDeepSeek({
      apiKey: process.env.DEEPSEEK_API_KEY,
      baseURL: process.env.BASE_URL
    });

    // 创建流式响应
    const result = await streamText({
      model: deepseek('deepseek-v3'),
      messages,
      system: '你是一名专业的医生，擅长为用户提供线上问诊、推荐附近医院和科室，并能提供挂号链接。请用简明、专业的语言回答用户问题。',
      onFinish: async (message) => {
        // 保存AI回复到数据库
        await prisma.message.create({
          data: {
            role: 'assistant',
            content: message.text,
            conversationId: currentConversationId,
          }
        });
      }
    });

    // 返回流式响应
    return result.toDataStreamResponse();
    
  } catch (error) {
    console.error('聊天API错误:', error);
    return Response.json({ error: '聊天失败' }, { status: 500 });
  }
}
