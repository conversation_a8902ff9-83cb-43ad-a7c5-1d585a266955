import { auth } from '@clerk/nextjs/server';
import { prisma } from '../../../../lib/database';
import { NextResponse } from 'next/server';

export async function GET() {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    const conversations = await prisma.conversation.findMany({
      where: { userId },
      orderBy: { updatedAt: 'desc' },
    });

    return NextResponse.json(conversations);
  } catch (error) {
    console.error('获取对话列表失败:', error);
    return NextResponse.json({ error: '获取对话列表失败' }, { status: 500 });
  }
}

export async function POST() {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    // 检查用户是否存在，如果不存在则创建
    let user = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      // 创建新用户
      user = await prisma.user.create({
        data: {
          id: userId,
          email: `${userId}@default.com`, // 使用默认邮箱
        }
      });
    }

    const newConversation = await prisma.conversation.create({
      data: {
        title: '新对话',
        userId: userId,
      },
    });

    return NextResponse.json(newConversation);
  } catch (error) {
    console.error('创建对话失败:', error);
    return NextResponse.json({ error: '创建对话失败' }, { status: 500 });
  }
} 