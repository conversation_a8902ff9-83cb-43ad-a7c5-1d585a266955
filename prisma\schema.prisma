// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model User {
  id           String        @id @default(cuid())
  email     String    @unique
  firstName String?
  lastName  String?
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  conversations Conversation[]
}

model Conversation {
  id        String   @id @default(cuid())
  title     String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  userId    String
  user      User     @relation(fields: [userId], references: [id]) // 添加这行
  messages  Message[]
}

model Message {
  id             String       @id @default(cuid())
  role           String       // 'user' or 'assistant'
  content        String       @db.Text  // 修改这里，使用Text类型而不是默认的VarChar
  conversationId String
  conversation   Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
}

model Hospital {
  id      String  @id @default(cuid())
  name    String
  address String
  desc    String  @db.Text
  lat     Float
  lng     Float
} 