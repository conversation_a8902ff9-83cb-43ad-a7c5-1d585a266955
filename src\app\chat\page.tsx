'use client';

import { useUser } from '@clerk/nextjs';
import { useState, useEffect } from 'react';
import { marked } from 'marked';
import { useChat } from '@ai-sdk/react';
import { useRouter } from 'next/navigation';

interface Conversation {
  id: string;
  title: string;
  createdAt: string;
  updatedAt: string;
}

export default function ChatPage() {
  const { isSignedIn, user } = useUser();
  const router = useRouter();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [selectedConversationId, setSelectedConversationId] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentMessages, setCurrentMessages] = useState<any[]>([]);

  useEffect(() => {
    if (!isSignedIn) {
      router.push('/');
      return;
    }
    loadConversations();
  }, [isSignedIn, router]);

  const loadConversations = async () => {
    try {
      const response = await fetch('/api/conversations');
      if (response.ok) {
        const data = await response.json();
        setConversations(data);
        if (data.length > 0 && !selectedConversationId) {
          setSelectedConversationId(data[0].id);
        }
      }
    } catch (error) {
      console.error('加载对话失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadMessages = async (conversationId: string) => {
    try {
      const response = await fetch(`/api/conversations/${conversationId}/messages`);
      if (response.ok) {
        const data = await response.json();
        setCurrentMessages(data);
      }
    } catch (error) {
      console.error('加载消息失败:', error);
    }
  };

  useEffect(() => {
    if (selectedConversationId) {
      loadMessages(selectedConversationId);
    } else {
      setCurrentMessages([]);
    }
  }, [selectedConversationId]);

  const createNewConversation = async () => {
    try {
      const response = await fetch('/api/conversations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      if (response.ok) {
        const newConversation = await response.json();
        setConversations(prev => [newConversation, ...prev]);
        setSelectedConversationId(newConversation.id);
        setCurrentMessages([]);
        return newConversation.id;
      }
    } catch (error) {
      console.error('创建对话失败:', error);
    }
    return null;
  };

  const { messages, input, handleInputChange, handleSubmit, isLoading: chatIsLoading, setMessages } = useChat({
    api: '/api/chat',
    body: {
      conversationId: selectedConversationId,
    },
    onFinish: () => {
      // 只更新对话列表，不重新加载消息（避免重复显示）
      loadConversations();
    }
  });

  // 当切换对话时，清空当前的流式消息
  useEffect(() => {
    setMessages([]);
  }, [selectedConversationId, setMessages]);

  if (!isSignedIn) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-xl">请先登录</div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-xl">加载中...</div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gray-50">
      <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
        <div className="p-4 border-b border-gray-200">
          <button
            onClick={createNewConversation}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition"
          >
            开启新对话
          </button>
        </div>

        <div className="flex-1 overflow-y-auto">
          {conversations.length === 0 ? (
            <div className="p-4 text-center text-gray-500">暂无对话记录</div>
          ) : (
            <div className="p-2">
              {conversations.map((conversation) => (
                <div
                  key={conversation.id}
                  onClick={() => setSelectedConversationId(conversation.id)}
                  className={`p-3 rounded-lg cursor-pointer transition ${
                    selectedConversationId === conversation.id
                      ? 'bg-blue-100 text-blue-700'
                      : 'hover:bg-gray-100'
                  }`}
                >
                  <div className="font-medium truncate">{conversation.title}</div>
                  <div className="text-xs text-gray-500 mt-1">
                    {new Date(conversation.updatedAt).toLocaleString()}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      <div className="flex-1 flex flex-col">
        <div className="flex-1 overflow-y-auto p-4">
          {messages.length === 0 && currentMessages.length === 0 ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center text-gray-500">
                <div className="text-2xl mb-2">🤖</div>
                <div className="text-lg font-medium">AI医疗助手</div>
                <div className="text-sm">我是您的专属医疗顾问，有什么可以帮您的吗？</div>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {/* 显示历史消息 */}
              {currentMessages.map((message) => (
                <div
                  key={`history-${message.id}`}
                  className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div
                    className={`max-w-[70%] px-3 py-0 rounded-lg ${
                      message.role === 'user'
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-200 text-gray-800'
                    }`}
                  >
                    <div
                      className="whitespace-pre-wrap leading-normal"
                      style={{
                        lineHeight: 'normal'
                      }}
                      dangerouslySetInnerHTML={{
                        __html: String(marked(message.content)).replace(/<p>/g, '<div>').replace(/<\/p>/g, '</div>')
                      }}
                    />
                  </div>
                </div>
              ))}

              {/* 显示当前对话的流式消息 */}
              {messages.map((message) => (
                <div
                  key={`stream-${message.id}`}
                  className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div
                    className={`max-w-[70%] px-3 py-1.5 rounded-lg ${
                      message.role === 'user'
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-200 text-gray-800'
                    }`}
                  >
                    <div
                      className="whitespace-pre-wrap leading-normal"
                      style={{
                        lineHeight: 'normal'
                      }}
                      dangerouslySetInnerHTML={{
                        __html: String(marked(message.content)).replace(/<p>/g, '<div>').replace(/<\/p>/g, '</div>')
                      }}
                    />
                  </div>
                </div>
              ))}

              {chatIsLoading && (
                <div className="flex justify-start">
                  <div className="bg-gray-200 text-gray-800 p-3 rounded-lg">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        <div className="border-t border-gray-200 p-4">
          <form onSubmit={handleSubmit} className="flex space-x-2">
            <input
              className="flex-1 p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={input}
              placeholder="输入您的问题..."
              onChange={handleInputChange}
              disabled={chatIsLoading}
            />
            <button
              type="submit"
              disabled={chatIsLoading || !input.trim()}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition disabled:opacity-50 disabled:cursor-not-allowed"
            >
              发送
            </button>
          </form>
        </div>
      </div>
    </div>
  );
}