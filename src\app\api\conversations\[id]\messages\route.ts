import { prisma } from '../../../../../../lib/database';
import { auth } from '@clerk/nextjs/server';
import { NextResponse } from 'next/server';

export async function GET(
  request: Request,
  { params }: { params: { id: string } } // 移除了Promise包装
) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return Response.json({ error: '未授权' }, { status: 401 });
    }

    // 直接解构params，不需要await
    const { id } = params;

    // 验证对话属于当前用户
    const conversation = await prisma.conversation.findFirst({
      where: {
        id: id,
        userId: userId,
      },
    });

    if (!conversation) {
      return NextResponse.json({ error: '对话不存在' }, { status: 404 });
    }

    const messages = await prisma.message.findMany({
      where: {
        conversationId: id, // 现在可以直接使用id
      },
      orderBy: {
        createdAt: 'asc',
      },
    });

    return NextResponse.json(messages);
  } catch (error) {
    console.error('获取对话消息失败:', error);
    return Response.json({ error: '获取对话消息失败' }, { status: 500 });
  }
} 